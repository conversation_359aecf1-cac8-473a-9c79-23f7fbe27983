<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

class QuizAttempt extends Model
{
    use HasUuids;

    protected $fillable = [
        'enrollment_lecture_id',
        'quiz_id',
        'user_id',
        'selected_answer',
        'is_correct',
        'score'
    ];

    protected $casts = [
        'is_correct' => 'boolean'
    ];

    public function lecture()
    {
        return $this->belongsTo(EnrollmentLecture::class, 'enrollment_lecture_id');
    }

    public function quiz()
    {
        return $this->belongsTo(Quiz::class);
    }
}

