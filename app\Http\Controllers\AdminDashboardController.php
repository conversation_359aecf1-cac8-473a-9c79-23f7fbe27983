<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\User;
use App\Models\Enrollment;
use App\Models\Category;
use App\Models\CourseComment;
use App\Models\Level;
use App\Models\Lecture;
use App\Models\Quiz;
use App\Models\EnrollmentLecture;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class AdminDashboardController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'period' => 'nullable|in:7,30,90,365',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $period = $request->period ?? 30;
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : Carbon::now()->subDays($period);
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();

        try {
            $stats = [
                'overview' => $this->getOverviewStats(),
                'user_analytics' => $this->getUserAnalytics($startDate, $endDate),
                'course_analytics' => $this->getCourseAnalytics($startDate, $endDate),
                'enrollment_analytics' => $this->getEnrollmentAnalytics($startDate, $endDate),
                'performance_metrics' => $this->getPerformanceMetrics($startDate, $endDate),
                'recent_activities' => $this->getRecentActivities(),
                'top_performers' => $this->getTopPerformers(),
                'system_health' => $this->getSystemHealth(),
            ];

            return response()->json($stats);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch dashboard data', 'message' => $e->getMessage()], 500);
        }
    }

    private function getOverviewStats()
    {
        return [
            'total_users' => User::count(),
            'total_students' => User::role('Student')->count(),
            'total_admins' => User::role('Admin')->count(),
            'total_courses' => Course::count(),
            'published_courses' => Course::where('status', 'published')->count(),
            'draft_courses' => Course::where('status', 'draft')->count(),
            'total_enrollments' => Enrollment::count(),
            'active_enrollments' => Enrollment::where('status', 'active')->count(),
            'completed_enrollments' => Enrollment::where('status', 'completed')->count(),
            'total_lectures' => Lecture::count(),
            'total_quizzes' => Quiz::count(),
            'total_categories' => Category::count(),
            'total_levels' => Level::count(),
            'pending_comments' => CourseComment::where('is_approved', false)->count(),
            'total_comments' => CourseComment::count(),
        ];
    }

    private function getUserAnalytics($startDate, $endDate)
    {
        $userRegistrations = User::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $usersByRole = User::selectRaw('
                CASE
                    WHEN EXISTS (SELECT 1 FROM model_has_roles WHERE model_id = users.id AND role_id = (SELECT id FROM roles WHERE name = "Admin")) THEN "Admin"
                    WHEN EXISTS (SELECT 1 FROM model_has_roles WHERE model_id = users.id AND role_id = (SELECT id FROM roles WHERE name = "Student")) THEN "Student"
                    ELSE "No Role"
                END as role,
                COUNT(*) as count
            ')
            ->groupBy('role')
            ->get();

        $activeUsers = User::whereHas('enrollments', function($query) use ($startDate, $endDate) {
                $query->whereBetween('updated_at', [$startDate, $endDate]);
            })
            ->count();

        return [
            'registrations_chart' => $userRegistrations,
            'users_by_role' => $usersByRole,
            'active_users' => $activeUsers,
            'user_growth_rate' => $this->calculateGrowthRate(User::class, $startDate, $endDate),
        ];
    }

    private function getCourseAnalytics($startDate, $endDate)
    {
        $courseCreations = Course::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $coursesByCategory = Course::with('category')
            ->selectRaw('category_id, COUNT(*) as count')
            ->groupBy('category_id')
            ->get()
            ->map(function($item) {
                return [
                    'category' => $item->category->name ?? 'Uncategorized',
                    'count' => $item->count
                ];
            });

        $coursesByLevel = Course::with('levels')
            ->get()
            ->flatMap(function($course) {
                return $course->levels->map(function($level) {
                    return $level->name;
                });
            })
            ->countBy()
            ->map(function($count, $level) {
                return ['level' => $level, 'count' => $count];
            })
            ->values();

        $popularCourses = Course::withCount('enrollments')
            ->orderBy('enrollments_count', 'desc')
            ->limit(10)
            ->get(['id', 'title', 'enrollments_count']);

        return [
            'creations_chart' => $courseCreations,
            'courses_by_category' => $coursesByCategory,
            'courses_by_level' => $coursesByLevel,
            'popular_courses' => $popularCourses,
            'course_growth_rate' => $this->calculateGrowthRate(Course::class, $startDate, $endDate),
        ];
    }

    private function getEnrollmentAnalytics($startDate, $endDate)
    {
        $enrollmentTrends = Enrollment::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $enrollmentsByStatus = Enrollment::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        $completionRates = Enrollment::selectRaw('
                AVG(progress) as average_progress,
                COUNT(CASE WHEN status = "completed" THEN 1 END) as completed,
                COUNT(*) as total
            ')
            ->first();

        $monthlyEnrollments = Enrollment::whereBetween('created_at', [Carbon::now()->subMonths(12), Carbon::now()])
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as count')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return [
            'enrollment_trends' => $enrollmentTrends,
            'enrollments_by_status' => $enrollmentsByStatus,
            'completion_rate' => $completionRates->total > 0 ? ($completionRates->completed / $completionRates->total) * 100 : 0,
            'average_progress' => round($completionRates->average_progress ?? 0, 2),
            'monthly_enrollments' => $monthlyEnrollments,
            'enrollment_growth_rate' => $this->calculateGrowthRate(Enrollment::class, $startDate, $endDate),
        ];
    }

    private function getPerformanceMetrics($startDate, $endDate)
    {
        $lectureCompletions = EnrollmentLecture::where('status', 'completed')
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->count();

        $averageTimeToComplete = Enrollment::where('status', 'completed')
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->selectRaw('AVG(DATEDIFF(updated_at, created_at)) as avg_days')
            ->first();

        $quizAttempts = DB::table('quiz_attempts')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $averageQuizScore = DB::table('quiz_attempts')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->avg('score');

        return [
            'lecture_completions' => $lectureCompletions,
            'average_completion_time_days' => round($averageTimeToComplete->avg_days ?? 0, 1),
            'quiz_attempts' => $quizAttempts,
            'average_quiz_score' => round($averageQuizScore ?? 0, 2),
        ];
    }

    private function getRecentActivities()
    {
        $recentEnrollments = Enrollment::with(['user:id,name', 'course:id,title'])
            ->latest()
            ->limit(10)
            ->get(['id', 'user_id', 'course_id', 'created_at', 'status']);

        $recentCourses = Course::with(['creator:id,name'])
            ->latest()
            ->limit(5)
            ->get(['id', 'title', 'created_by', 'created_at', 'status']);

        $recentUsers = User::latest()
            ->limit(5)
            ->get(['id', 'name', 'email', 'created_at']);

        return [
            'recent_enrollments' => $recentEnrollments,
            'recent_courses' => $recentCourses,
            'recent_users' => $recentUsers,
        ];
    }

    private function getTopPerformers()
    {
        $topStudents = User::role('Student')
            ->withCount(['enrollments as completed_courses' => function($query) {
                $query->where('status', 'completed');
            }])
            ->orderBy('completed_courses', 'desc')
            ->limit(10)
            ->get(['id', 'name', 'email', 'completed_courses']);

        $topCourseCreators = User::withCount('createdCourses')
            ->having('created_courses_count', '>', 0)
            ->orderBy('created_courses_count', 'desc')
            ->limit(10)
            ->get(['id', 'name', 'email', 'created_courses_count']);

        return [
            'top_students' => $topStudents,
            'top_course_creators' => $topCourseCreators,
        ];
    }

    private function getSystemHealth()
    {
        $diskUsage = disk_free_space('/') / disk_total_space('/') * 100;
        $memoryUsage = memory_get_usage(true) / 1024 / 1024; // MB

        return [
            'disk_usage_percentage' => round(100 - $diskUsage, 2),
            'memory_usage_mb' => round($memoryUsage, 2),
            'database_size' => $this->getDatabaseSize(),
            'total_files' => $this->getTotalMediaFiles(),
        ];
    }

    private function calculateGrowthRate($model, $startDate, $endDate)
    {
        $currentPeriod = $model::whereBetween('created_at', [$startDate, $endDate])->count();
        $previousPeriod = $model::whereBetween('created_at', [
            $startDate->copy()->subDays($endDate->diffInDays($startDate)),
            $startDate
        ])->count();

        if ($previousPeriod == 0) {
            return $currentPeriod > 0 ? 100 : 0;
        }

        return round((($currentPeriod - $previousPeriod) / $previousPeriod) * 100, 2);
    }

    private function getDatabaseSize()
    {
        try {
            $size = DB::select("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'size_mb' FROM information_schema.tables WHERE table_schema = DATABASE()")[0]->size_mb ?? 0;
            return $size . ' MB';
        } catch (\Exception $e) {
            return 'N/A';
        }
    }

    private function getTotalMediaFiles()
    {
        try {
            return DB::table('media')->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    public function getNotificationsSummary()
    {
        if (!Auth::user()->hasRole('Admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return response()->json([
            'pending_comments' => CourseComment::where('is_approved', false)->count(),
            'unread_notifications' => Auth::user()->unreadNotifications->count(),
            'recent_comments' => CourseComment::with(['user:id,name', 'course:id,title'])
                ->where('is_approved', false)
                ->latest()
                ->limit(5)
                ->get()
        ]);
    }
}



