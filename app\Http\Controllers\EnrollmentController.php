<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Enrollment;
use App\Models\EnrollmentLecture;
use App\Models\Quiz;
use App\Models\QuizAttempt;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class EnrollmentController extends Controller
{
    public function enroll(Request $request)
    {
        $data = $request->validate([
            'course_id' => 'required|exists:courses,id'
        ]);

        $user = $request->user();

        // Check if already enrolled
        $existingEnrollment = Enrollment::where('course_id', $data['course_id'])
            ->where('user_id', $user->id)
            ->first();

        if ($existingEnrollment) {
            return response()->json(['message' => 'Already enrolled in this course'], 409);
        }

        DB::beginTransaction();
        try {
            $enrollment = Enrollment::create([
                'course_id' => $data['course_id'],
                'user_id' => $user->id,
                'status' => 'active',
                'progress' => 0
            ]);

            $course = Course::with('sections.lectures')->find($data['course_id']);

            foreach ($course->sections as $sectionIndex => $section) {
                $enrollmentSection = $enrollment->sections()->create([
                    'section_id' => $section->id,
                    'status' => $sectionIndex === 0 ? 'unlocked' : 'locked'
                ]);

                foreach ($section->lectures as $lectureIndex => $lecture) {
                    $enrollmentSection->lectures()->create([
                        'lecture_id' => $lecture->id,
                        'status' => ($lectureIndex === 0 && $sectionIndex === 0) ? 'unlocked' : 'locked',
                        'video_watched' => false,
                        'quiz_completed' => false
                    ]);
                }
            }

            DB::commit();
            return response()->json(['message' => 'Enrolled successfully', 'enrollment' => $enrollment]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Enrollment failed'], 500);
        }
    }

    public function watchVideo(Request $request, $enrollmentLectureId)
    {
        $enrollmentLecture = EnrollmentLecture::findOrFail($enrollmentLectureId);

        if ($enrollmentLecture->status !== 'unlocked') {
            return response()->json(['error' => 'Lecture is not unlocked'], 403);
        }

        $enrollmentLecture->update(['video_watched' => true]);
        $this->checkLectureCompletion($enrollmentLecture);

        return response()->json([
            'message' => 'Video marked as watched',
            'lecture' => $enrollmentLecture->fresh()
        ]);
    }

    public function completeQuiz(Request $request, $enrollmentLectureId)
    {
        $request->validate([
            'quiz_id' => 'required|exists:quizzes,id',
            'selected_answer' => 'required|in:a,b,c,d'
        ]);

        $enrollmentLecture = EnrollmentLecture::findOrFail($enrollmentLectureId);

        if ($enrollmentLecture->status !== 'unlocked') {
            return response()->json(['error' => 'Lecture is not unlocked'], 403);
        }

        // Get the quiz to check correct answer
        $quiz = Quiz::findOrFail($request->quiz_id);
        $isCorrect = $quiz->correct_answer === $request->selected_answer;

        // Record quiz attempt
        $quizAttempt = $enrollmentLecture->quizAttempts()->create([
            'quiz_id' => $request->quiz_id,
            'user_id' => $request->user()->id,
            'selected_answer' => $request->selected_answer,
            'is_correct' => $isCorrect,
            'score' => $isCorrect ? 100 : 0
        ]);

        // Check if all quizzes for this lecture are completed
        $lectureQuizzes = $enrollmentLecture->lecture->quizzes;
        $completedQuizzes = $enrollmentLecture->quizAttempts()->whereIn('quiz_id', $lectureQuizzes->pluck('id'))->count();

        $allQuizzesCompleted = $completedQuizzes >= $lectureQuizzes->count();

        if ($allQuizzesCompleted) {
            // Calculate final score for the lecture
            $correctAnswers = $enrollmentLecture->quizAttempts()
                ->whereIn('quiz_id', $lectureQuizzes->pluck('id'))
                ->where('is_correct', true)
                ->count();

            $totalQuestions = $lectureQuizzes->count();
            $finalScore = round(($correctAnswers / $totalQuestions) * 100, 2);

            $enrollmentLecture->update([
                'quiz_completed' => true,
                'quiz_score' => $finalScore
            ]);

            $this->checkLectureCompletion($enrollmentLecture);

            return response()->json([
                'message' => 'All quizzes completed!',
                'quiz_attempt' => $quizAttempt,
                'is_correct' => $isCorrect,
                'lecture_completed' => true,
                'final_results' => [
                    'correct_answers' => $correctAnswers,
                    'total_questions' => $totalQuestions,
                    'score' => $finalScore,
                    'percentage' => "{$correctAnswers}/{$totalQuestions}"
                ],
                'lecture' => $enrollmentLecture->fresh()
            ]);
        }

        return response()->json([
            'message' => 'Quiz answer submitted',
            'quiz_attempt' => $quizAttempt,
            'is_correct' => $isCorrect,
            'correct_answer' => $isCorrect ? null : $quiz->correct_answer, // Show correct answer if wrong
            'lecture_completed' => false,
            'progress' => [
                'completed' => $completedQuizzes + 1,
                'total' => $lectureQuizzes->count()
            ]
        ]);
    }

    protected function checkLectureCompletion(EnrollmentLecture $enrollmentLecture)
    {
        if ($enrollmentLecture->isCompleted()) {
            $enrollmentLecture->markAsCompleted();
            $this->unlockNextContent($enrollmentLecture);
        }
    }

    protected function unlockNextContent(EnrollmentLecture $completedLecture)
    {
        $section = $completedLecture->section;

        // Find next lecture in same section
        $nextLecture = $section->lectures()
            ->where('status', 'locked')
            ->whereHas('lecture', function($query) use ($completedLecture) {
                $query->where('order', '>', $completedLecture->lecture->order);
            })
            ->orderBy('created_at')
            ->first();

        if ($nextLecture) {
            // Unlock next lecture in same section
            $nextLecture->update(['status' => 'unlocked']);
        } else {
            // Check if section is completed
            $section->markAsCompleted();

            if ($section->isCompleted()) {
                $this->unlockNextSection($section);
            }
        }

        // Update enrollment progress
        $section->enrollment->calculateProgress();

        // Check if enrollment is completed
        if ($section->enrollment->isCompleted()) {
            $section->enrollment->markAsCompleted();
        }
    }

    protected function unlockNextSection($completedSection)
    {
        $enrollment = $completedSection->enrollment;

        $nextSection = $enrollment->sections()
            ->where('status', 'locked')
            ->whereHas('section', function($query) use ($completedSection) {
                $query->where('order', '>', $completedSection->section->order);
            })
            ->orderBy('created_at')
            ->first();

        if ($nextSection) {
            $nextSection->update(['status' => 'unlocked']);

            // Unlock first lecture in next section
            $firstLecture = $nextSection->lectures()
                ->orderBy('created_at')
                ->first();

            if ($firstLecture) {
                $firstLecture->update(['status' => 'unlocked']);
            }
        }
    }

    public function getProgress($enrollmentId)
    {
        $enrollment = Enrollment::with([
            'course',
            'sections.section',
            'sections.lectures.lecture'
        ])->findOrFail($enrollmentId);

        $progress = $enrollment->calculateProgress();

        return response()->json([
            'enrollment' => $enrollment,
            'progress' => $progress,
            'sections' => $enrollment->sections->map(function($section) {
                return [
                    'id' => $section->id,
                    'title' => $section->section->title,
                    'status' => $section->status,
                    'progress' => $section->getProgressPercentage(),
                    'lectures' => $section->lectures->map(function($lecture) {
                        return [
                            'id' => $lecture->id,
                            'title' => $lecture->lecture->title,
                            'status' => $lecture->status,
                            'video_watched' => $lecture->video_watched,
                            'quiz_completed' => $lecture->quiz_completed,
                            'completed_at' => $lecture->completed_at
                        ];
                    })
                ];
            })
        ]);
    }

    public function myEnrollments(Request $request)
    {
        $enrollments = Enrollment::with([
            'course.category',
            'course.subCategory',
            'sections.section',
            'sections.lectures'
        ])
        ->where('user_id', $request->user()->id)
        ->latest()
        ->paginate(10);

        return response()->json($enrollments);
    }

    public function unenroll(Request $request, $enrollmentId)
    {
        $enrollment = Enrollment::where('id', $enrollmentId)
            ->where('user_id', $request->user()->id)
            ->firstOrFail();

        $enrollment->update(['status' => 'dropped']);

        return response()->json(['message' => 'Successfully unenrolled from course']);
    }

    public function getLecture($enrollmentLectureId)
    {
        $enrollmentLecture = EnrollmentLecture::with([
            'lecture.quizzes',
            'section.section',
            'section.enrollment.course'
        ])->findOrFail($enrollmentLectureId);

        return response()->json([
            'lecture' => $enrollmentLecture,
            'can_access' => $enrollmentLecture->status === 'unlocked' || $enrollmentLecture->status === 'completed'
        ]);
    }

    public function getCourseMaterials($enrollmentId)
    {
        $enrollment = Enrollment::with('course.materials')->findOrFail($enrollmentId);

        return response()->json($enrollment->course->materials);
    }

    public function getQuizAttempts($enrollmentLectureId)
    {
        $attempts = QuizAttempt::where('enrollment_lecture_id', $enrollmentLectureId)
            ->with('quiz')
            ->latest()
            ->get();

        return response()->json($attempts);
    }

    public function getLectureQuizResults($enrollmentLectureId)
    {
        $enrollmentLecture = EnrollmentLecture::with([
            'lecture.quizzes',
            'quizAttempts.quiz'
        ])->findOrFail($enrollmentLectureId);

        $quizResults = $enrollmentLecture->quizAttempts->map(function($attempt) {
            return [
                'question' => $attempt->quiz->question,
                'selected_answer' => $attempt->selected_answer,
                'correct_answer' => $attempt->quiz->correct_answer,
                'is_correct' => $attempt->is_correct,
                'choices' => [
                    'a' => $attempt->quiz->choice_a,
                    'b' => $attempt->quiz->choice_b,
                    'c' => $attempt->quiz->choice_c,
                    'd' => $attempt->quiz->choice_d,
                ]
            ];
        });

        $correctCount = $enrollmentLecture->quizAttempts->where('is_correct', true)->count();
        $totalCount = $enrollmentLecture->lecture->quizzes->count();

        return response()->json([
            'quiz_results' => $quizResults,
            'summary' => [
                'correct' => $correctCount,
                'total' => $totalCount,
                'score' => $enrollmentLecture->quiz_score,
                'percentage' => "{$correctCount}/{$totalCount}"
            ]
        ]);
    }

    public function getDashboardStats(Request $request)
    {
        $user = $request->user();

        $stats = [
            'total_enrollments' => Enrollment::where('user_id', $user->id)->count(),
            'completed_courses' => Enrollment::where('user_id', $user->id)->where('status', 'completed')->count(),
            'in_progress_courses' => Enrollment::where('user_id', $user->id)->where('status', 'active')->count(),
            'total_lectures_completed' => EnrollmentLecture::whereHas('section', function($query) use ($user) {
                $query->whereHas('enrollment', function($q) use ($user) {
                    $q->where('user_id', $user->id);
                });
            })->where('status', 'completed')->count(),
            'average_progress' => Enrollment::where('user_id', $user->id)->avg('progress') ?? 0
        ];

        return response()->json($stats);
    }

    public function getRecentActivity(Request $request)
    {
        $recentLectures = EnrollmentLecture::whereHas('section', function($query) use ($request) {
            $query->whereHas('enrollment', function($q) use ($request) {
                $q->where('user_id', $request->user()->id);
            });
        })
        ->where('status', 'completed')
        ->with(['lecture', 'section.section', 'section.enrollment.course'])
        ->latest('completed_at')
        ->limit(10)
        ->get();

        return response()->json($recentLectures);
    }
}




