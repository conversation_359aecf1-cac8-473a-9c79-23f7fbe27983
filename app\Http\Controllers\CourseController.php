<?php

namespace App\Http\Controllers;

use App\Http\Requests\CourseStoreRequest;
use App\Http\Requests\CourseUpdateRequest;
use App\Http\Requests\CourseWizardRequest;
use App\Http\Resources\CourseResource;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;

class CourseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            //Gate::authorize('viewAny', Course::class);

            $request->validate([
                'search' => 'nullable|string|max:255',
                'category_id' => 'nullable|uuid|exists:categories,id',
                'sub_category_id' => 'nullable|uuid|exists:sub_categories,id',
                'level_id' => 'nullable|uuid|exists:levels,id',
                'instructor_name' => 'nullable|string|max:255',
                'duration_min' => 'nullable|integer|min:0',
                'duration_max' => 'nullable|integer|min:0',
                'sort_by' => 'nullable|in:title,created_at,duration,instructor_name',
                'sort_order' => 'nullable|in:asc,desc',
                'per_page' => 'nullable|integer|min:1|max:100',
            ]);

            $courses = Course::with([
                'levels',
                'category',
                'subCategory',
                'creator',
                'sections.lectures.quizzes',
                'materials'
            ])
            ->when($request->search, function ($query, $search) {
                return $query->where(function ($query) use ($search) {
                    $query->where('title', 'like', "%{$search}%")
                          ->orWhere('description', 'like', "%{$search}%")
                          ->orWhere('objectives', 'like', "%{$search}%")
                          ->orWhere('instructor_name', 'like', "%{$search}%")
                          ->orWhereHas('category', function ($query) use ($search) {
                              $query->where('name', 'like', "%{$search}%");
                          })
                          ->orWhereHas('subCategory', function ($query) use ($search) {
                              $query->where('name', 'like', "%{$search}%");
                          })
                          ->orWhereHas('levels', function ($query) use ($search) {
                              $query->where('name', 'like', "%{$search}%");
                          });
                });
            })
            ->when($request->category_id, function ($query, $categoryId) {
                return $query->where('category_id', $categoryId);
            })
            ->when($request->sub_category_id, function ($query, $subCategoryId) {
                return $query->where('sub_category_id', $subCategoryId);
            })
            ->when($request->level_id, function ($query, $levelId) {
                return $query->whereHas('levels', function ($query) use ($levelId) {
                    $query->where('levels.id', $levelId);
                });
            })
            ->when($request->instructor_name, function ($query, $instructorName) {
                return $query->where('instructor_name', 'like', "%{$instructorName}%");
            })
            ->when($request->duration_min, function ($query, $durationMin) {
                return $query->where('duration', '>=', $durationMin);
            })
            ->when($request->duration_max, function ($query, $durationMax) {
                return $query->where('duration', '<=', $durationMax);
            })
            ->when($request->sort_by, function ($query, $sortBy) use ($request) {
                $sortOrder = $request->sort_order ?? 'asc';
                return $query->orderBy($sortBy, $sortOrder);
            }, function ($query) {
                return $query->latest();
            })
            ->paginate($request->per_page ?? 10);

            return CourseResource::collection($courses);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch courses', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CourseStoreRequest $request)
    {
        try {
            Gate::authorize('create', Course::class);

            $course = Course::create($request->validated());

            if ($request->has('levels')) {
                $course->levels()->attach($request->levels);
            }

            if ($request->hasFile('image')) {
                $course->addMediaFromRequest('image')->toMediaCollection('course_image');
            }

            if ($request->hasFile('video')) {
                $course->addMediaFromRequest('video')->toMediaCollection('course_trailer');
            }

            return new CourseResource($course->load(['levels', 'category', 'subCategory',  'creator', 'levels', 'sections.lectures.quizzes']));
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to create course', 'message' => $e->getMessage()], 500);
        }
    }

    public function storeWizard(CourseWizardRequest $request)
    {
        DB::beginTransaction();

        try {
            Gate::authorize('create', Course::class);
            // Validate unique section orders
            if ($request->filled('sections')) {
                $sectionOrders = collect($request->sections)->pluck('order')->toArray();
                if (count($sectionOrders) !== count(array_unique($sectionOrders))) {
                    return response()->json(['error' => 'Section orders must be unique'], 422);
                }

                // Validate unique lecture orders within each section
                if ($request->filled('sections')) {
                    foreach ($request->sections as $sectionIndex => $sectionData) {
                        if (!empty($sectionData['lectures'])) {
                            $lectureOrders = collect($sectionData['lectures'])->pluck('order')->toArray();
                            if (count($lectureOrders) !== count(array_unique($lectureOrders))) {
                                return response()->json([
                                    'error' => "Lecture orders must be unique within section " . ($sectionIndex + 1),
                                    'errors' => [
                                        "sections.{$sectionIndex}.lectures" => ["Duplicate lecture orders found in this section"]
                                    ]
                                ], 422);
                            }
                        }
                    }
                }
            }

            $courseData = $request->only([
                'title',
                'description',
                'objectives',
                'category_id',
                'sub_category_id',
                'duration',
                'welcome_message',
                'congratulations_message',
                'instructor_name',
                'instructor_description',
                'instructor_facebook',
                'instructor_instagram',
                'instructor_twitter'
            ]);
            $course = Course::create($courseData);

            if ($request->has('levels')) {
                $course->levels()->attach($request->levels);
            }

            if ($request->hasFile('image')) {
                $course->addMediaFromRequest('image')->toMediaCollection('course_image');
            }
            if ($request->hasFile('video')) {
                $course->addMediaFromRequest('video')->toMediaCollection('course_trailer');
            }

            if ($request->filled('sections')) {
                foreach ($request->sections as $sectionData) {
                    $lecturesData = $sectionData['lectures'] ?? [];
                    unset($sectionData['lectures']);

                    $section = $course->sections()->create($sectionData);

                    foreach ($lecturesData as $lectureData) {
                        $lectureFiles = [];
                        if (isset($lectureData['image_file'])) $lectureFiles['image_file'] = $lectureData['image_file'];
                        if (isset($lectureData['video_file'])) $lectureFiles['video_file'] = $lectureData['video_file'];
                        if (isset($lectureData['lecture_note_file'])) $lectureFiles['lecture_note_file'] = $lectureData['lecture_note_file'];
                        unset($lectureData['image_file'], $lectureData['video_file'], $lectureData['lecture_note_file']);

                        $lecture = $section->lectures()->create($lectureData);

                        // Handle lecture media
                        if (isset($lectureFiles['image_file'])) {
                            $lecture->addMedia($lectureFiles['image_file'])->toMediaCollection('lecture_image');
                        }
                        if (isset($lectureFiles['video_file'])) {
                            $lecture->addMedia($lectureFiles['video_file'])->toMediaCollection('lecture_video');
                        }
                        if (isset($lectureFiles['lecture_note_file'])) {
                            $lecture->addMedia($lectureFiles['lecture_note_file'])->toMediaCollection('lecture_note_file');
                        }

                        // Handle quizzes
                        if (!empty($lectureData['quizzes'])) {
                            foreach ($lectureData['quizzes'] as $quizData) {
                                $lecture->quizzes()->create($quizData);
                            }
                        }
                    }
                }
            }

            if ($request->filled('materials')) {
                foreach ($request->materials as $materialData) {
                    $file = $materialData['file'] ?? null;
                    unset($materialData['file']);

                    $material = $course->materials()->create($materialData);

                    if ($file) {
                        $material->addMedia($file)->toMediaCollection('course_materials');
                    }
                }
            }

            DB::commit();

            return response()->json([
                'message' => 'Course, sections, lectures, and quizzes created successfully',
                'course' => new CourseResource($course->load(['category', 'subCategory', 'levels', 'sections.lectures.quizzes']))
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Failed to create course wizard',
                'message' => $e->getMessage()
            ], 500);
        }
    }


    /**
     * Display the specified resource.
     */
    public function show(Course $course)
    {
        try {
            Gate::authorize('view', $course);
            return new CourseResource($course->load(['levels', 'category', 'subCategory', 'creator', 'sections.lectures.quizzes']));
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch course', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CourseUpdateRequest $request, Course $course)
    {
        DB::beginTransaction();

        try {
            Gate::authorize('update', $course);

            // Update course data
            $course->update($request->validated());

            // Sync levels if provided
            if ($request->filled('levels')) {
                $course->levels()->sync($request->levels);
            }

            // Update image
            if ($request->hasFile('image')) {
                $course->clearMediaCollection('course_image');
                $course->addMediaFromRequest('image')->toMediaCollection('course_image');
            }

            // Update video
            if ($request->hasFile('video')) {
                $course->clearMediaCollection('course_trailer');
                $course->addMediaFromRequest('video')->toMediaCollection('course_trailer');
            }

            DB::commit();

            return new CourseResource(
                $course->fresh(['category', 'creator', 'subCategory', 'levels', 'sections.lectures.quizzes'])
            );
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'error'   => 'Failed to update course',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Course $course)
    {
        DB::beginTransaction();

        try {
            Gate::authorize('delete', $course);

            $course->delete();

            DB::commit();
            return response()->json(['message' => 'Course deleted successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error'   => 'Failed to delete course',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getFilterOptions()
    {
        try {
            $categories = \App\Models\Category::select('id', 'name')->get();
            $subCategories = \App\Models\SubCategory::select('id', 'name', 'category_id')->with('category:id,name')->get();
            $levels = \App\Models\Level::select('id', 'name')->get();

            $instructors = Course::select('instructor_name')
                ->whereNotNull('instructor_name')
                ->distinct()
                ->pluck('instructor_name');

            $durationRange = Course::selectRaw('MIN(duration) as min_duration, MAX(duration) as max_duration')
                ->first();

            return response()->json([
                'categories' => $categories,
                'sub_categories' => $subCategories,
                'levels' => $levels,
                'instructors' => $instructors,
                'duration_range' => $durationRange,
                'sort_options' => [
                    ['value' => 'title', 'label' => 'Title'],
                    ['value' => 'created_at', 'label' => 'Created Date'],
                    ['value' => 'duration', 'label' => 'Duration'],
                    ['value' => 'instructor_name', 'label' => 'Instructor Name'],
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch filter options', 'message' => $e->getMessage()], 500);
        }
    }
}


