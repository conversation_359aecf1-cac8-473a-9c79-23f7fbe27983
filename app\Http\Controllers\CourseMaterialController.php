<?php

namespace App\Http\Controllers;

use App\Http\Requests\CourseMaterialStoreRequest;
use App\Http\Requests\CourseMaterialUpdateRequest;
use App\Http\Resources\CourseMaterialResource;
use App\Models\CourseMaterial;
use App\Models\Enrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Spatie\Activitylog\Models\Activity;

class CourseMaterialController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            Gate::authorize('viewAny', CourseMaterial::class);
            $materials = CourseMaterial::with('course', 'creator')->latest()->paginate(10);
            return CourseMaterialResource::collection($materials);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch materials', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CourseMaterialStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            Gate::authorize('create', CourseMaterial::class);

            $material = CourseMaterial::create($request->validated());

            if ($request->hasFile('file')) {
                $material->addMediaFromRequest('file')->toMediaCollection('material_file');
            }

            DB::commit();
            return new CourseMaterialResource($material->fresh('course', 'creator'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to create material', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(CourseMaterial $course_material)
    {
        try {
            Gate::authorize('view', $course_material);
            return new CourseMaterialResource($course_material->load('course', 'creator'));
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch material', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CourseMaterialUpdateRequest $request, CourseMaterial $course_material)
    {
        DB::beginTransaction();
        try {
            Gate::authorize('update', $course_material);

            $course_material->update($request->validated());

            if ($request->hasFile('file')) {
                $course_material->clearMediaCollection('material_file');
                $course_material->addMediaFromRequest('file')->toMediaCollection('material_file');
            }

            DB::commit();
            return new CourseMaterialResource($course_material->fresh('course', 'creator'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to update material', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CourseMaterial $course_material)
    {
        DB::beginTransaction();
        try {
            Gate::authorize('delete', $course_material);

            $course_material->delete();

            DB::commit();
            return response()->json(['message' => 'Material deleted successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to delete material', 'message' => $e->getMessage()], 500);
        }
    }

    public function download($materialId)
    {
        try {
            $material = CourseMaterial::findOrFail($materialId);

            // Check if user is enrolled in the course
            $user = Auth::user();
            $isEnrolled = Enrollment::where('user_id', $user->id)
                ->where('course_id', $material->course_id)
                ->where('status', 'active')
                ->exists();

            if (!$isEnrolled && !$user->hasPermissionTo('read_course_material')) {
                return response()->json(['error' => 'Access denied. You must be enrolled in this course.'], 403);
            }

            $mediaFile = $material->getFirstMedia('material_file');

            if (!$mediaFile) {
                return response()->json(['error' => 'File not found'], 404);
            }

            // Log download activity
            // Activity::log('Downloaded course material')
            //     ->performedOn($material)
            //     ->causedBy($user);

            return response()->download($mediaFile->getPath(), $mediaFile->name);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Download failed', 'message' => $e->getMessage()], 500);
        }
    }
}
