<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Course;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $firstCategoryId = Category::first()->id ?? null;
        if (!$firstCategoryId) {
            return;
        }
        $data = [
            [
                'title' => 'Introduction to Programming',
                'description' => 'Learn the basics of programming using Python.',
                'objectives' => 'Understand programming concepts and write simple programs.',
                'duration' => 30,
                'category_id' => $firstCategoryId,
            ],
            [
                'title' => 'Web Development Bootcamp',
                'description' => 'Become a full-stack web developer with HTML, CSS, JavaScript, and more.',
                'objectives' => 'Build responsive websites and web applications from scratch.',
                'duration' => 30,
                'category_id' => $firstCategoryId,
            ],
            [
                'title' => 'Data Science Fundamentals',
                'description' => 'Explore data analysis, visualization, and machine learning techniques.',
                'objectives' => 'Analyze datasets and create predictive models.',
                'duration' => 30,
                'category_id' => $firstCategoryId,
            ],

            [
                'title' => 'Digital Marketing 101',
                'description' => 'Learn the essentials of digital marketing and online advertising.',
                'objectives' => 'Create effective marketing campaigns and measure their success.',
                'duration' => 30,
                'category_id' => $firstCategoryId,
            ],
            [
                'title' => 'Graphic Design Basics',
                'description' => 'Discover the principles of graphic design and visual communication.',
                'objectives' => 'Create visually appealing designs using industry-standard tools.',
                'duration' => 30,
                'category_id' => $firstCategoryId,
            ],

            [
                'title' => 'Project Management Essentials',
                'description' => 'Master the fundamentals of project management and team collaboration.',
                'objectives' => 'Plan, execute, and close projects successfully.',
                'duration' => 30,
                'category_id' => $firstCategoryId,
            ],
        ];

        foreach ($data as $course) {
            Course::create($course);
        }
    }
}
