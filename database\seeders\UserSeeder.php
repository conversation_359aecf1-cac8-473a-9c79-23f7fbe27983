<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $admin = User::updateOrCreate([
            'email' => '<EMAIL>',
            'phone' => '251900000000',
            'username' => 'admin',
        ], [
            'name' => 'Admin',
            'verified_at' => now(),
            'password' => bcrypt('123456')
        ]);

        $admin->assignRole('Admin');

        $student = User::updateOrCreate([
            'email' => '<EMAIL>',
            'phone' => '251911111111',
            'username' => 'student',
        ], [
            'name' => 'Student',
            'verified_at' => now(),
            'password' => bcrypt('123456')
        ]);

        $student->assignRole('Student');
    }
}
