<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

class EnrollmentLecture extends Model
{
    use HasUuids;

    protected $fillable = [
        'enrollment_section_id',
        'lecture_id',
        'status',
        'video_watched',
        'quiz_completed',
        'quiz_score',
        'completed_at'
    ];

    protected $casts = [
        'video_watched' => 'boolean',
        'quiz_completed' => 'boolean',
        'completed_at' => 'datetime'
    ];

    public function section()
    {
        return $this->belongsTo(EnrollmentSection::class, 'enrollment_section_id');
    }

    public function lecture()
    {
        return $this->belongsTo(Lecture::class);
    }

    public function quizAttempts()
    {
        return $this->hasMany(QuizAttempt::class);
    }

    public function isCompleted()
    {
        return $this->video_watched && $this->quiz_completed;
    }

    public function markAsCompleted()
    {
        if ($this->isCompleted() && $this->status !== 'completed') {
            $this->update([
                'status' => 'completed',
                'completed_at' => now()
            ]);
        }
    }
}

