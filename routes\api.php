<?php

use App\Http\Controllers\ActivityLogController;
use App\Http\Controllers\AdminDashboardController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CourseCommentController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\CourseMaterialController;
use App\Http\Controllers\EnrollmentController;
use App\Http\Controllers\LectureController;
use App\Http\Controllers\LevelController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\OTPController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\QuizController;
use App\Http\Controllers\ResetPasswordController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\RolePermissionController;
use App\Http\Controllers\SectionController;
use App\Http\Controllers\SubCategoryController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => 'api',
], function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('subscribe', [AuthController::class, 'register']);
    Route::post('unsubscribe', [AuthController::class, 'unsubscribe']);

    /**
     *  Other AuthController routes
     */
    Route::post('create-password', [AuthController::class, 'create_password'])->middleware('auth:api');
    Route::post('update-profile-image', [AuthController::class, 'update_profile_image'])->middleware('auth:api');
    Route::post('remove-profile-image', [AuthController::class, 'remove_profile_image'])->middleware('auth:api');

    Route::post('update-profile', [AuthController::class, 'update_profile'])->middleware('auth:api');

    Route::post('reset-password', [ResetPasswordController::class, 'reset_password']);
    Route::post('change-password', [ResetPasswordController::class, 'change_password'])->middleware('auth:api');

    Route::post('verify-otp', [OTPController::class, 'verify_otp']);
    Route::post('resend-otp', [OTPController::class, 'resend_otp']);

    Route::group([
        'middleware' => 'auth:api',
    ], function () {

        /**
         * Common Endpoints */
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::post('/refresh', [AuthController::class, 'refresh']);
        Route::get('/my-profile', [AuthController::class, 'profile']);

        Route::get('my-activity-logs', [ActivityLogController::class, 'myActivityLog']);


        Route::post('test-notification', [NotificationController::class, 'testNotification']);

        Route::get('my-notifications', [NotificationController::class, 'getNotification']);
        Route::post('read-all-notifications', [NotificationController::class, 'readNotifications']);
        Route::post('read-notification/{id}', [NotificationController::class, 'readNotification']);


        /**
         * Admin Endpoints */
        Route::get('admin/dashboard', [AdminDashboardController::class, 'index']);
        Route::resource('roles', RoleController::class);
        Route::get('permissions', [PermissionController::class, 'index']);
        Route::get('admin/notifications-summary', [AdminDashboardController::class, 'getNotificationsSummary']);


        Route::post('roles/{role}/attach-permissions', [RolePermissionController::class, 'attachPermission']);
        Route::post('roles/{role}/detach-permissions', [RolePermissionController::class, 'detachPermission']);
        Route::post('assign-role/{user}', [RoleController::class, 'assignRole']);

        Route::get('get-activity-logs', [ActivityLogController::class, 'index']);

        Route::apiResource('categories', CategoryController::class);
        Route::apiResource('sub-categories', SubCategoryController::class);
        Route::apiResource('levels', LevelController::class);
        Route::apiResource('courses', CourseController::class);
        Route::get('courses-filter-options', [CourseController::class, 'getFilterOptions']);
        Route::post('store-wizard', [CourseController::class, 'storeWizard']);
        Route::apiResource('sections', SectionController::class);
        Route::apiResource('lecture', LectureController::class);
        Route::apiResource('course-materials', CourseMaterialController::class);
        Route::apiResource('quizzes', QuizController::class);


        /**
         * Student Endpoints
         */

        // Enrollment Management
        Route::post('enroll', [EnrollmentController::class, 'enroll']);
        Route::get('my-enrollments', [EnrollmentController::class, 'myEnrollments']);
        Route::get('enrollments/{enrollmentId}', [EnrollmentController::class, 'getProgress']);
        Route::delete('enrollments/{enrollmentId}', [EnrollmentController::class, 'unenroll']);

        // Learning Progress
        Route::post('enrollments/lecture/{enrollmentLectureId}/watch-video', [EnrollmentController::class, 'watchVideo']);
        Route::post('enrollments/lecture/{enrollmentLectureId}/complete-quiz', [EnrollmentController::class, 'completeQuiz']);
        Route::get('enrollments/lecture/{enrollmentLectureId}', [EnrollmentController::class, 'getLecture']);
        Route::get('enrollments/lecture/{enrollmentLectureId}/quiz-results', [EnrollmentController::class, 'getLectureQuizResults']);

        // Course Materials
        Route::get('enrollments/{enrollmentId}/materials', [EnrollmentController::class, 'getCourseMaterials']);
        Route::get('course-materials/{materialId}/download', [CourseMaterialController::class, 'download']);

        // Quiz & Assessment
        Route::get('lectures/{lectureId}/quiz', [QuizController::class, 'getLectureQuiz']);
        Route::get('enrollments/lecture/{enrollmentLectureId}/quiz-attempts', [EnrollmentController::class, 'getQuizAttempts']);

        // Student Dashboard
        Route::get('dashboard/stats', [EnrollmentController::class, 'getDashboardStats']);
        Route::get('dashboard/recent-activity', [EnrollmentController::class, 'getRecentActivity']);

        // Course Comments
        Route::get('courses/{course}/comments', [CourseCommentController::class, 'index']);
        Route::post('courses/{course}/comments', [CourseCommentController::class, 'store']);
        Route::get('comments/{comment}', [CourseCommentController::class, 'show']);
        Route::put('comments/{comment}', [CourseCommentController::class, 'update']);
        Route::delete('comments/{comment}', [CourseCommentController::class, 'destroy']);
        Route::patch('comments/{comment}/approve', [CourseCommentController::class, 'approve']);
        Route::patch('comments/{comment}/reject', [CourseCommentController::class, 'reject']);
        Route::get('my-comments', [CourseCommentController::class, 'getUserComments']);
        Route::get('admin/pending-comments', [CourseCommentController::class, 'getPendingComments']);
    });
});







