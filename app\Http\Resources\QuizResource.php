<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class QuizResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'lecture_id' => $this->lecture_id,
            'question' => $this->question,
            'choice_a' => $this->choice_a,
            'choice_b' => $this->choice_b,
            'choice_c' => $this->choice_c,
            'choice_d' => $this->choice_d,
            'correct_answer' => $this->correct_answer,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
