<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Section>
 */
class SectionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $firstUserId = User::first()->id;
        return [
            'title' => fake()->sentence(1),
            'description' => fake()->paragraph(),
            'created_by' => $firstUserId,
        ];
    }
}
