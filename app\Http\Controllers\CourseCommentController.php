<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\CourseComment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use App\Notifications\CommentStatusNotification;
use App\Notifications\NewCommentNotification;
use Illuminate\Support\Facades\Notification;

class CourseCommentController extends Controller
{
    public function index(Request $request, Course $course)
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100',
            'include_unapproved' => 'nullable|boolean'
        ]);

        $perPage = $request->per_page ?? 15;
        $includeUnapproved = $request->include_unapproved && Auth::user()?->hasRole('Admin');

        $query = $course->comments()
            ->parentComments()
            ->with(['user:id,name,email', 'approvedReplies.user:id,name,email']);

        if (!$includeUnapproved) {
            $query->approved();
        }

        $comments = $query->latest()->paginate($perPage);

        return response()->json($comments);
    }

    public function store(Request $request, Course $course)
    {
        $request->validate([
            'comment' => 'required|string|min:3|max:1000',
            'parent_id' => [
                'nullable',
                'uuid',
                Rule::exists('course_comments', 'id')->where(function ($query) use ($course) {
                    $query->where('course_id', $course->id);
                })
            ]
        ]);

        $user = Auth::user();
        if (!$user->hasRole('Admin')) {
            $isEnrolled = $user->enrollments()->where('course_id', $course->id)->exists();
            if (!$isEnrolled) {
                return response()->json(['message' => 'You must be enrolled in this course to comment'], 403);
            }
        }

        // Set approval status - you can change this logic as needed
        $isApproved = $user->hasRole('Admin') ? true : false; // Require approval for students

        $comment = CourseComment::create([
            'course_id' => $course->id,
            'user_id' => $user->id,
            'parent_id' => $request->parent_id,
            'comment' => $request->comment,
            'is_approved' => $isApproved
        ]);

        $comment->load(['user:id,name,email', 'parent.user:id,name', 'course:id,title']);

        // Notify admins if comment needs approval
        if (!$isApproved) {
            $admins = User::role('Admin')->get();
            Notification::send($admins, new NewCommentNotification($comment));
        }

        return response()->json([
            'message' => $isApproved ? 'Comment added successfully' : 'Comment submitted for approval',
            'comment' => $comment,
            'needs_approval' => !$isApproved
        ], 201);
    }

    public function show(CourseComment $comment)
    {
        $comment->load(['user:id,name,email', 'course:id,title', 'parent.user:id,name', 'approvedReplies.user:id,name,email']);

        return response()->json($comment);
    }

    public function update(Request $request, CourseComment $comment)
    {
        $user = Auth::user();

        if (!$comment->canBeEditedBy($user)) {
            return response()->json(['message' => 'Unauthorized to edit this comment'], 403);
        }

        $request->validate([
            'comment' => 'required|string|min:3|max:1000',
            'is_approved' => 'nullable|boolean'
        ]);

        $updateData = ['comment' => $request->comment];

        // Only admins can change approval status
        if ($user->hasRole('Admin') && $request->has('is_approved')) {
            $updateData['is_approved'] = $request->is_approved;
        }

        $comment->update($updateData);
        $comment->load(['user:id,name,email', 'approvedReplies.user:id,name,email']);

        return response()->json([
            'message' => 'Comment updated successfully',
            'comment' => $comment
        ]);
    }

    public function destroy(CourseComment $comment)
    {
        $user = Auth::user();
        
        if (!$comment->canBeDeletedBy($user)) {
            return response()->json(['message' => 'Unauthorized to delete this comment'], 403);
        }

        $comment->delete();

        return response()->json(['message' => 'Comment deleted successfully']);
    }

    public function approve(CourseComment $comment)
    {
        if (!Auth::user()->hasRole('Admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $comment->update(['is_approved' => true]);
        $comment->load(['user', 'course']);

        // Notify the comment author
        $comment->user->notify(new CommentStatusNotification(
            $comment,
            'approved',
            Auth::user()->name
        ));

        return response()->json([
            'message' => 'Comment approved successfully',
            'comment' => $comment
        ]);
    }

    public function reject(CourseComment $comment)
    {
        if (!Auth::user()->hasRole('Admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $comment->update(['is_approved' => false]);
        $comment->load(['user', 'course']);

        // Notify the comment author
        $comment->user->notify(new CommentStatusNotification(
            $comment,
            'rejected',
            Auth::user()->name
        ));

        return response()->json([
            'message' => 'Comment rejected successfully',
            'comment' => $comment
        ]);
    }

    public function getUserComments(Request $request)
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100'
        ]);

        $perPage = $request->per_page ?? 15;
        $user = Auth::user();

        $comments = $user->courseComments()
            ->with(['course:id,title', 'parent.user:id,name'])
            ->latest()
            ->paginate($perPage);

        return response()->json($comments);
    }

    public function getPendingComments(Request $request)
    {
        if (!Auth::user()->hasRole('Admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100'
        ]);

        $perPage = $request->per_page ?? 15;

        $comments = CourseComment::where('is_approved', false)
            ->with(['user:id,name,email', 'course:id,title'])
            ->latest()
            ->paginate($perPage);

        return response()->json($comments);
    }
}

