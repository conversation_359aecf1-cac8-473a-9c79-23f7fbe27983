<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Section extends Model implements HasMedia
{
    use HasUuids, HasFactory, InteractsWithMedia;
    protected $fillable = [
        'course_id',
        'title',
        'description',
        'order',
    ];

    protected $appends = ['image', 'video'];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function getImageAttribute()
    {
        return $this->getFirstMediaUrl('section_image') ?? null;
    }

    public function getVideoAttribute()
    {
        return $this->getFirstMediaUrl('section_video') ?? null;
    }

    public function lectures()
    {
        return $this->hasMany(Lecture::class);
    }


    protected static function boot()
    {
        parent::boot();

        static::creating(function ($section) {
            if (Auth::check()) {
                $section->created_by = Auth::id();
            }
        });
    }
}
